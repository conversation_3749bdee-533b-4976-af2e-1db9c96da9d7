import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_tr.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('tr'),
  ];

  /// No description provided for @appTitle.
  ///
  /// In en, this message translates to:
  /// **'SpyOut'**
  String get appTitle;

  /// No description provided for @createRoom.
  ///
  /// In en, this message translates to:
  /// **'Create Room'**
  String get createRoom;

  /// No description provided for @joinRoom.
  ///
  /// In en, this message translates to:
  /// **'Join Room'**
  String get joinRoom;

  /// No description provided for @roomCode.
  ///
  /// In en, this message translates to:
  /// **'Room Code'**
  String get roomCode;

  /// No description provided for @playerName.
  ///
  /// In en, this message translates to:
  /// **'Player Name'**
  String get playerName;

  /// No description provided for @startGame.
  ///
  /// In en, this message translates to:
  /// **'Start Game'**
  String get startGame;

  /// No description provided for @waitingForPlayers.
  ///
  /// In en, this message translates to:
  /// **'Waiting for players...'**
  String get waitingForPlayers;

  /// No description provided for @yourRole.
  ///
  /// In en, this message translates to:
  /// **'Your Role'**
  String get yourRole;

  /// No description provided for @civilian.
  ///
  /// In en, this message translates to:
  /// **'Civilian'**
  String get civilian;

  /// No description provided for @spy.
  ///
  /// In en, this message translates to:
  /// **'Spy'**
  String get spy;

  /// No description provided for @location.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// No description provided for @timeRemaining.
  ///
  /// In en, this message translates to:
  /// **'Time Remaining'**
  String get timeRemaining;

  /// No description provided for @accusePlayer.
  ///
  /// In en, this message translates to:
  /// **'Accuse Player'**
  String get accusePlayer;

  /// No description provided for @guessLocation.
  ///
  /// In en, this message translates to:
  /// **'Guess Location'**
  String get guessLocation;

  /// No description provided for @vote.
  ///
  /// In en, this message translates to:
  /// **'Vote'**
  String get vote;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @gameOver.
  ///
  /// In en, this message translates to:
  /// **'Game Over'**
  String get gameOver;

  /// No description provided for @winner.
  ///
  /// In en, this message translates to:
  /// **'Winner'**
  String get winner;

  /// No description provided for @score.
  ///
  /// In en, this message translates to:
  /// **'Score'**
  String get score;

  /// No description provided for @nextRound.
  ///
  /// In en, this message translates to:
  /// **'Next Round'**
  String get nextRound;

  /// No description provided for @newGame.
  ///
  /// In en, this message translates to:
  /// **'New Game'**
  String get newGame;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @turkish.
  ///
  /// In en, this message translates to:
  /// **'Turkish'**
  String get turkish;

  /// No description provided for @players.
  ///
  /// In en, this message translates to:
  /// **'Players'**
  String get players;

  /// No description provided for @round.
  ///
  /// In en, this message translates to:
  /// **'Round'**
  String get round;

  /// No description provided for @accusation.
  ///
  /// In en, this message translates to:
  /// **'Accusation'**
  String get accusation;

  /// No description provided for @voting.
  ///
  /// In en, this message translates to:
  /// **'Voting'**
  String get voting;

  /// No description provided for @spyWins.
  ///
  /// In en, this message translates to:
  /// **'Spy Wins!'**
  String get spyWins;

  /// No description provided for @civiliansWin.
  ///
  /// In en, this message translates to:
  /// **'Civilians Win!'**
  String get civiliansWin;

  /// No description provided for @correctGuess.
  ///
  /// In en, this message translates to:
  /// **'Correct Guess!'**
  String get correctGuess;

  /// No description provided for @wrongGuess.
  ///
  /// In en, this message translates to:
  /// **'Wrong Guess!'**
  String get wrongGuess;

  /// No description provided for @unanimousVote.
  ///
  /// In en, this message translates to:
  /// **'Unanimous Vote Required'**
  String get unanimousVote;

  /// No description provided for @accusationFailed.
  ///
  /// In en, this message translates to:
  /// **'Accusation Failed'**
  String get accusationFailed;

  /// No description provided for @timeUp.
  ///
  /// In en, this message translates to:
  /// **'Time\'s Up!'**
  String get timeUp;

  /// No description provided for @askQuestion.
  ///
  /// In en, this message translates to:
  /// **'Ask a Question'**
  String get askQuestion;

  /// No description provided for @answer.
  ///
  /// In en, this message translates to:
  /// **'Answer'**
  String get answer;

  /// No description provided for @question.
  ///
  /// In en, this message translates to:
  /// **'Question'**
  String get question;

  /// No description provided for @chat.
  ///
  /// In en, this message translates to:
  /// **'Chat'**
  String get chat;

  /// No description provided for @send.
  ///
  /// In en, this message translates to:
  /// **'Send'**
  String get send;

  /// No description provided for @viewSpyLocations.
  ///
  /// In en, this message translates to:
  /// **'View Spy\'s Locations'**
  String get viewSpyLocations;

  /// No description provided for @spyLocationsList.
  ///
  /// In en, this message translates to:
  /// **'Spy\'s Seen Locations'**
  String get spyLocationsList;

  /// No description provided for @noLocationsYet.
  ///
  /// In en, this message translates to:
  /// **'No locations seen yet'**
  String get noLocationsYet;

  /// No description provided for @ultimateSpyGame.
  ///
  /// In en, this message translates to:
  /// **'The Ultimate Spy Game'**
  String get ultimateSpyGame;

  /// No description provided for @hostInfo.
  ///
  /// In en, this message translates to:
  /// **'You will be the host of this room. Share the room code with other players to let them join.'**
  String get hostInfo;

  /// No description provided for @joinRoomInfo.
  ///
  /// In en, this message translates to:
  /// **'Ask the host for the room code. Room codes are 6 characters long.'**
  String get joinRoomInfo;

  /// No description provided for @connectionError.
  ///
  /// In en, this message translates to:
  /// **'Connection error: {error}'**
  String connectionError(String error);

  /// No description provided for @goBack.
  ///
  /// In en, this message translates to:
  /// **'Go Back'**
  String get goBack;

  /// No description provided for @roomNotFound.
  ///
  /// In en, this message translates to:
  /// **'Room not found\\nRoom ID: {roomId}'**
  String roomNotFound(String roomId);

  /// No description provided for @roomCodeCopied.
  ///
  /// In en, this message translates to:
  /// **'Room code copied to clipboard'**
  String get roomCodeCopied;

  /// No description provided for @host.
  ///
  /// In en, this message translates to:
  /// **'Host'**
  String get host;

  /// No description provided for @needMinPlayers.
  ///
  /// In en, this message translates to:
  /// **'Need at least 4 players to start'**
  String get needMinPlayers;

  /// No description provided for @failedToCreateRoom.
  ///
  /// In en, this message translates to:
  /// **'Failed to create room: {error}'**
  String failedToCreateRoom(String error);

  /// No description provided for @failedToJoinRoom.
  ///
  /// In en, this message translates to:
  /// **'Failed to join room: {error}'**
  String failedToJoinRoom(String error);

  /// No description provided for @failedToStartGame.
  ///
  /// In en, this message translates to:
  /// **'Failed to start game: {error}'**
  String failedToStartGame(String error);

  /// No description provided for @accusesText.
  ///
  /// In en, this message translates to:
  /// **' accuses '**
  String get accusesText;

  /// No description provided for @ofBeingSpyText.
  ///
  /// In en, this message translates to:
  /// **' of being the spy!'**
  String get ofBeingSpyText;

  /// No description provided for @timeRemainingLabel.
  ///
  /// In en, this message translates to:
  /// **'Time Remaining'**
  String get timeRemainingLabel;

  /// No description provided for @enterNameAndCode.
  ///
  /// In en, this message translates to:
  /// **'Enter your name and room code to join an existing game'**
  String get enterNameAndCode;

  /// No description provided for @gameAlreadyStarted.
  ///
  /// In en, this message translates to:
  /// **'Game has already started'**
  String get gameAlreadyStarted;

  /// No description provided for @roomIsFull.
  ///
  /// In en, this message translates to:
  /// **'Room is full'**
  String get roomIsFull;

  /// No description provided for @roomNotFoundError.
  ///
  /// In en, this message translates to:
  /// **'Room not found'**
  String get roomNotFoundError;

  /// No description provided for @viewAllLocations.
  ///
  /// In en, this message translates to:
  /// **'View All Locations'**
  String get viewAllLocations;

  /// No description provided for @accused.
  ///
  /// In en, this message translates to:
  /// **'ACCUSED'**
  String get accused;

  /// No description provided for @pleaseEnterName.
  ///
  /// In en, this message translates to:
  /// **'Please enter your name'**
  String get pleaseEnterName;

  /// No description provided for @enterNameToCreate.
  ///
  /// In en, this message translates to:
  /// **'Enter your name to create a new game room'**
  String get enterNameToCreate;

  /// No description provided for @whereDoYouThink.
  ///
  /// In en, this message translates to:
  /// **'Where do you think you are?'**
  String get whereDoYouThink;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @changeLanguage.
  ///
  /// In en, this message translates to:
  /// **'Change Language'**
  String get changeLanguage;

  /// No description provided for @allLocations.
  ///
  /// In en, this message translates to:
  /// **'All Locations'**
  String get allLocations;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @leaveGame.
  ///
  /// In en, this message translates to:
  /// **'Leave Game?'**
  String get leaveGame;

  /// No description provided for @leaveGameConfirm.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to leave the game?'**
  String get leaveGameConfirm;

  /// No description provided for @leave.
  ///
  /// In en, this message translates to:
  /// **'Leave'**
  String get leave;

  /// No description provided for @languageChanged.
  ///
  /// In en, this message translates to:
  /// **'Language changed successfully'**
  String get languageChanged;

  /// No description provided for @selectPlayerToAccuse.
  ///
  /// In en, this message translates to:
  /// **'Who do you think is the spy?'**
  String get selectPlayerToAccuse;

  /// No description provided for @youAreAccused.
  ///
  /// In en, this message translates to:
  /// **'You are accused and cannot vote'**
  String get youAreAccused;

  /// No description provided for @failedToAccusePlayer.
  ///
  /// In en, this message translates to:
  /// **'Failed to accuse player: {error}'**
  String failedToAccusePlayer(String error);

  /// No description provided for @howToPlay.
  ///
  /// In en, this message translates to:
  /// **'How to Play'**
  String get howToPlay;

  /// No description provided for @howToPlayTitle.
  ///
  /// In en, this message translates to:
  /// **'How to Play SpyOut?'**
  String get howToPlayTitle;

  /// No description provided for @gameObjective.
  ///
  /// In en, this message translates to:
  /// **'Game Objective'**
  String get gameObjective;

  /// No description provided for @gameObjectiveText.
  ///
  /// In en, this message translates to:
  /// **'SpyOut is a hidden identity and deduction game. Players are divided into two groups: Civilians and Spy.'**
  String get gameObjectiveText;

  /// No description provided for @civilianObjective.
  ///
  /// In en, this message translates to:
  /// **'Civilian Objective'**
  String get civilianObjective;

  /// No description provided for @civilianObjectiveText.
  ///
  /// In en, this message translates to:
  /// **'• Find and accuse the spy\n• Ask questions to expose the spy\n• Cooperate with other civilians'**
  String get civilianObjectiveText;

  /// No description provided for @spyObjective.
  ///
  /// In en, this message translates to:
  /// **'Spy Objective'**
  String get spyObjective;

  /// No description provided for @spyObjectiveText.
  ///
  /// In en, this message translates to:
  /// **'• Keep your identity hidden\n• Try to guess the location\n• Act like a civilian'**
  String get spyObjectiveText;

  /// No description provided for @gameSetup.
  ///
  /// In en, this message translates to:
  /// **'Game Setup'**
  String get gameSetup;

  /// No description provided for @gameSetupText.
  ///
  /// In en, this message translates to:
  /// **'• Minimum 4, maximum 10 players\n• One player creates a room and shares the code\n• Other players join using the room code\n• Host starts the game'**
  String get gameSetupText;

  /// No description provided for @gameFlow.
  ///
  /// In en, this message translates to:
  /// **'Game Flow'**
  String get gameFlow;

  /// No description provided for @gameFlowText.
  ///
  /// In en, this message translates to:
  /// **'• When the game starts, roles are secretly assigned\n• Civilians learn the location, spy doesn\'t\n• Players take turns asking questions and answering\n• Any player can make an accusation at any time'**
  String get gameFlowText;

  /// No description provided for @questionPhase.
  ///
  /// In en, this message translates to:
  /// **'Question Phase'**
  String get questionPhase;

  /// No description provided for @questionPhaseText.
  ///
  /// In en, this message translates to:
  /// **'• Each player can ask questions to other players\n• Questions should be location-related but not too obvious\n• Spy must answer carefully since they don\'t know the location\n• Civilians should try to catch the spy'**
  String get questionPhaseText;

  /// No description provided for @accusationPhase.
  ///
  /// In en, this message translates to:
  /// **'Accusation Phase'**
  String get accusationPhase;

  /// No description provided for @accusationPhaseText.
  ///
  /// In en, this message translates to:
  /// **'• Any player can make an accusation at any time\n• The accused player cannot vote\n• All other players vote\n• Decision must be unanimous'**
  String get accusationPhaseText;

  /// No description provided for @winConditions.
  ///
  /// In en, this message translates to:
  /// **'Win Conditions'**
  String get winConditions;

  /// No description provided for @winConditionsText.
  ///
  /// In en, this message translates to:
  /// **'• Civilians win: If they correctly accuse the spy\n• Spy wins: If they correctly guess the location\n• Spy wins: If they are wrongly accused\n• Spy wins: If time runs out'**
  String get winConditionsText;

  /// No description provided for @tips.
  ///
  /// In en, this message translates to:
  /// **'Tips'**
  String get tips;

  /// No description provided for @tipsText.
  ///
  /// In en, this message translates to:
  /// **'• Civilians: Don\'t ask obvious questions, don\'t give away the location\n• Civilians: Listen carefully to other players\' answers\n• Spy: Ask smart questions to learn the location\n• Spy: Act natural to avoid suspicion\n• Everyone: Don\'t forget the time limit!'**
  String get tipsText;

  /// No description provided for @earnBonusPoints.
  ///
  /// In en, this message translates to:
  /// **'Earn Bonus Points!'**
  String get earnBonusPoints;

  /// No description provided for @watchAdForBonusPoints.
  ///
  /// In en, this message translates to:
  /// **'Watch a short ad to earn bonus points and support the game development.'**
  String get watchAdForBonusPoints;

  /// No description provided for @watchAd.
  ///
  /// In en, this message translates to:
  /// **'Watch Ad'**
  String get watchAd;

  /// No description provided for @skip.
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// No description provided for @earnBonus.
  ///
  /// In en, this message translates to:
  /// **'Earn Bonus'**
  String get earnBonus;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'tr'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'tr':
      return AppLocalizationsTr();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
